.App {
  min-height: 100vh;
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

.tab-navigation {
  display: flex;
  background: transparent;
  padding: 0;
  margin: 0;
}

.tab-button {
  flex: 1;
  padding: 15px 20px;
  border: none;
  background: rgba(255, 255, 255, 0.3);
  color: white;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.tab-button:first-child {
  border-radius: 0;
}

.tab-button.active {
  background: rgba(255, 255, 255, 0.9);
  color: #333;
}

.tab-button:hover:not(.active) {
  background: rgba(255, 255, 255, 0.4);
}

.tab-icon {
  font-size: 16px;
}

.main-content {
  padding: 40px 20px;
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.header-section {
  text-align: center;
  margin-bottom: 40px;
}

.page-title {
  color: #000;
  font-size: 32px;
  font-weight: 700;
  letter-spacing: 8px;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tab-content {
  width: 80%;
  background: white;
  border-radius: 12px;
  padding: 36px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  min-height: 600px;
}

.tab-placeholder {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.tab-placeholder h2 {
  color: #333;
  margin-bottom: 20px;
}

/* Responsive */
@media (max-width: 768px) {
  .tab-button {
    font-size: 12px;
    padding: 12px 10px;
  }
  
  .page-title {
    font-size: 32px;
    letter-spacing: 4px;
  }
}
