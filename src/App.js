import { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, useLocation, useNavigate } from 'react-router-dom';
import './App.css';
import InfoDeclare from './components/InfoDeclare';
import InfoApply from './components/InfoApply';
import InfoService from './components/InfoService';
import ProcessSearch from './components/ProcessSearch';
import SideMenu from './components/SideMenu';
import ServiceEvaluation from './components/ServiceEvaluation';
import SystemComplaint from './components/SystemComplaint';

function MainContent() {
  const location = useLocation();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState(0);

  const tabs = [
    { id: 0, title: '信息化申报"一件事"', icon: '🎓', path: '/' },
    { id: 1, title: '信息化资源申请"一件事"', icon: '📄', path: '/' },
    { id: 2, title: '信息化服务"一窗口"', icon: '🏢', path: '/' }
  ];

  // 根据路径设置对应的tab
  useEffect(() => {
    if (location.pathname === '/') {
      // 主页保持当前tab状态
    } else {
      // 其他页面不改变tab状态，但可以通过URL参数控制
      const urlParams = new URLSearchParams(location.search);
      const tabParam = urlParams.get('tab');
      if (tabParam) {
        setActiveTab(parseInt(tabParam));
      }
    }
  }, [location]);

  const handleTabClick = (tabId) => {
    setActiveTab(tabId);
    // 如果当前不在主页，跳转到主页并设置tab参数
    if (location.pathname !== '/') {
      navigate(`/?tab=${tabId}`);
    }
  };

  return (
    <>
      <div className="tab-navigation">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            className={`tab-button ${activeTab === tab.id ? 'active' : ''}`}
            onClick={() => handleTabClick(tab.id)}
          >
            <span className="tab-icon">{tab.icon}</span>
            {tab.title}
          </button>
        ))}
      </div>

      <div className="main-content">
        <div className="header-section">
          <h1 className="page-title">
            {location.pathname === '/' ? '事项清单' : getPageTitle(location.pathname)}
          </h1>
        </div>

        <div className="tab-content">
          {renderContent(location.pathname, activeTab)}
        </div>
      </div>
    </>
  );
}

function getPageTitle(pathname) {
  const titles = {
    '/process-search': '进度查询',
    '/faq': '常见问题',
    '/service-evaluation': '服务评价',
    '/complaint': '系统投诉'
  };
  return titles[pathname] || '事项清单';
}

function renderContent(pathname, activeTab) {
  switch (pathname) {
    case '/process-search':
      return <ProcessSearch />;
    case '/faq':
      return (
        <div className="tab-placeholder">
          <h3>常见问题</h3>
          <p>此功能正在开发中...</p>
        </div>
      );
    case '/service-evaluation':
      return <ServiceEvaluation />;
    case '/complaint':
      return <SystemComplaint />;
    default:
      // 主页根据activeTab显示对应内容
      switch (activeTab) {
        case 0:
          return <InfoDeclare />;
        case 1:
          return <InfoApply />;
        case 2:
          return <InfoService />;
        default:
          return null;
      }
  }
}

function App() {
  return (
    <Router>
      <div className="App">
        <Routes>
          <Route path="/*" element={<MainContent />} />
        </Routes>
        <SideMenu />
      </div>
    </Router>
  );
}

export default App;
