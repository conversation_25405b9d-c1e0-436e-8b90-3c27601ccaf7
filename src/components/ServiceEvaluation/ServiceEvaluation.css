.service-evaluation {
  padding: 20px;
}

.evaluation-tabs .ant-tabs-nav {
  margin-bottom: 20px;
}

.evaluation-tabs .ant-tabs-tab {
  font-size: 16px;
  font-weight: 500;
}

.status-tabs {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.status-tab {
  padding: 8px 16px;
  background: #f5f5f5;
  border-radius: 20px;
  font-size: 14px;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
}

.status-tab.active {
  background: #1890ff;
  color: white;
}

.status-tab:hover:not(.active) {
  background: #e6f7ff;
  color: #1890ff;
}

.evaluation-table {
  background: white;
  border-radius: 8px;
}

.evaluation-table .ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
  color: #333;
  border-bottom: 2px solid #f0f0f0;
}

.evaluation-table .ant-table-tbody > tr > td {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.evaluation-table .ant-table-tbody > tr:hover > td {
  background: #f5f5f5;
}

.service-link {
  color: #1890ff;
  text-decoration: none;
}

.service-link:hover {
  color: #40a9ff;
  text-decoration: underline;
}

.evaluate-button {
  padding: 0;
  font-size: 14px;
  color: #1890ff;
}

.evaluate-button:hover {
  color: #40a9ff;
}

.tab-placeholder {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.tab-placeholder h3 {
  color: #333;
  margin-bottom: 20px;
  font-size: 18px;
}

/* 评价弹窗样式 */
.evaluation-modal .ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
}

.modal-content {
  padding: 20px 0;
}

.rating-section {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.rating-label {
  font-size: 16px;
  font-weight: 500;
  margin-right: 15px;
  color: #333;
}

.comment-section {
  margin-top: 20px;
}

.comment-section .ant-input {
  border-radius: 6px;
  border: 1px solid #d9d9d9;
}

.comment-section .ant-input:focus {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .service-evaluation {
    padding: 10px;
  }
  
  .status-tabs {
    flex-direction: column;
    gap: 10px;
  }
  
  .evaluation-table .ant-table-thead > tr > th,
  .evaluation-table .ant-table-tbody > tr > td {
    padding: 8px 12px;
    font-size: 12px;
  }
  
  .evaluation-tabs .ant-tabs-tab {
    font-size: 14px;
  }
  
  .evaluation-modal {
    margin: 0;
    max-width: 100vw;
  }
  
  .rating-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}