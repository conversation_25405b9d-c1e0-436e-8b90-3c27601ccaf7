import React, { useState, useEffect } from 'react';
import { Table, Tabs, Button, Modal, Rate, Input } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import './ServiceEvaluation.css';

const { TabPane } = Tabs;
const { TextArea } = Input;

function ServiceEvaluation() {
  const [activeTab, setActiveTab] = useState('evaluation');
  const navigate = useNavigate();
  const location = useLocation();
  
  // 其他状态保持不变...
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [rating, setRating] = useState(0);
  const [comment, setComment] = useState('');
  const [currentRecord, setCurrentRecord] = useState(null);

  // 根据来源设置正确的tab
  useEffect(() => {
    // 如果是从其他页面跳转过来的，保持evaluation tab
    setActiveTab('evaluation');
  }, [location]);

  const handleTabChange = (key) => {
    setActiveTab(key);
    switch (key) {
      case 'progress':
        navigate('/process-search');
        break;
      case 'evaluation':
        // 当前页面，不需要跳转
        break;
      case 'complaint':
        navigate('/complaint');
        break;
      default:
        break;
    }
  };

  // 其他方法保持不变...
  const evaluationData = [
    {
      key: '1',
      serialNumber: '1',
      serviceName: '马克思电子证照申请',
      businessNumber: '**********',
      serviceProcess: '电子证照申请',
      processTime: '2025-04-27',
      completionTime: '2025-04-27',
      duration: '1分17秒'
    },
    {
      key: '2',
      serialNumber: '2',
      serviceName: '马克思电子证照申请',
      businessNumber: '**********',
      serviceProcess: '电子证照申请',
      processTime: '2025-04-27',
      completionTime: '2025-04-27',
      duration: '6分25秒'
    },
    {
      key: '3',
      serialNumber: '3',
      serviceName: '马克思电子证照申请',
      businessNumber: 'YZ20250006',
      serviceProcess: '电子证照申请',
      processTime: '2025-04-27',
      completionTime: '2025-04-27',
      duration: '13秒'
    },
    {
      key: '4',
      serialNumber: '4',
      serviceName: '马克思电子证照申请',
      businessNumber: 'YZ20250005',
      serviceProcess: '电子证照申请',
      processTime: '2025-04-27',
      completionTime: '2025-04-27',
      duration: '1秒'
    },
    {
      key: '5',
      serialNumber: '5',
      serviceName: '马克思电子证照申请',
      businessNumber: 'YZ20250004',
      serviceProcess: '电子证照申请',
      processTime: '2025-04-27',
      completionTime: '2025-04-27',
      duration: '23秒'
    }
  ];

  const columns = [
    {
      title: '序号',
      dataIndex: 'serialNumber',
      key: 'serialNumber',
      width: 60,
      align: 'center'
    },
    {
      title: '事务名称',
      dataIndex: 'serviceName',
      key: 'serviceName',
      width: 180,
      render: (text) => (
        <a href="#" className="service-link">{text}</a>
      )
    },
    {
      title: '业务编号',
      dataIndex: 'businessNumber',
      key: 'businessNumber',
      width: 120
    },
    {
      title: '事务流程',
      dataIndex: 'serviceProcess',
      key: 'serviceProcess',
      width: 150
    },
    {
      title: '处理时间',
      dataIndex: 'processTime',
      key: 'processTime',
      width: 120
    },
    {
      title: '办结时间',
      dataIndex: 'completionTime',
      key: 'completionTime',
      width: 120
    },
    {
      title: '耗时用时',
      dataIndex: 'duration',
      key: 'duration',
      width: 100
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      align: 'center',
      render: (_, record) => (
        <Button 
          type="link" 
          className="evaluate-button"
          onClick={() => handleEvaluate(record)}
        >
          评价
        </Button>
      )
    }
  ];

  const handleEvaluate = (record) => {
    setCurrentRecord(record);
    setIsModalVisible(true);
    setRating(0);
    setComment('');
  };

  const handleModalOk = () => {
    console.log('评价提交:', {
      record: currentRecord,
      rating,
      comment
    });
    setIsModalVisible(false);
  };

  const handleModalCancel = () => {
    setIsModalVisible(false);
  };

  return (
    <div className="service-evaluation">
      <Tabs 
        activeKey={activeTab} 
        onChange={handleTabChange}
        className="evaluation-tabs"
      >
        <TabPane tab="进度查询" key="progress">
          <div className="tab-placeholder">
            <h3>进度查询</h3>
            <p>正在跳转到进度查询页面...</p>
          </div>
        </TabPane>
        
        <TabPane tab="服务评价" key="evaluation">
          <div className="status-tabs">
            <div className="status-tab active">
              待评价 (5)
            </div>
            <div className="status-tab">
              已评价 (8)
            </div>
          </div>
          
          <Table
            columns={columns}
            dataSource={evaluationData}
            pagination={false}
            className="evaluation-table"
            size="middle"
          />
        </TabPane>
        
        <TabPane tab="系统投诉" key="complaint">
          <div className="tab-placeholder">
            <h3>系统投诉</h3>
            <p>正在跳转到系统投诉页面...</p>
          </div>
        </TabPane>
      </Tabs>

      <Modal
        title="我要评价"
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        okText="提交"
        cancelText="取消"
        className="evaluation-modal"
        width={500}
      >
        <div className="modal-content">
          <div className="rating-section">
            <span className="rating-label">整体评价：</span>
            <Rate 
              value={rating} 
              onChange={setRating}
              style={{ fontSize: '24px' }}
            />
          </div>
          
          <div className="comment-section">
            <TextArea
              placeholder="请输入您的评价"
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              rows={4}
              maxLength={200}
              showCount
            />
          </div>
        </div>
      </Modal>
    </div>
  );
}

export default ServiceEvaluation;
