import React, { useState } from 'react';
import './InfoService.css';

function InfoService() {
  const [selectedItems, setSelectedItems] = useState([
    '基建维修施工网络确认单',
    '校园网端口开放申请',
    '校园网机房接入申请',
    'OA流程变更及删除申请',
    'Windows Server激活申请',
    '部门委托弱电建设及改造项目申请',
    '服务器托管申请',
    '部门自建子网站接入申请'
  ]);

  const serviceCategories = [
    {
      title: '账号权限服务',
      items: [
        'OA权限变更申请',
        '部门公告管理员权限变更',
        '会议签到权限变更',
        '统一通信平台部门管理员权限变更',
        'VPN临时账户申请',
        '上网认证账户申请',
        '统一身份认证临时账户申请',
        '非在编人员云盘账户申请'
      ]
    },
    {
      title: '网络服务',
      items: [
        '基建维修施工网络确认单',
        '校园网端口开放申请',
        '校园网机房接入申请'
      ]
    },
    {
      title: '信息服务',
      items: [
        'OA流程变更及删除申请',
        'Windows Server激活申请',
        '部门委托弱电建设及改造项目申请',
        '服务器托管申请',
        '部门自建子网站接入申请'
      ]
    }
  ];

  const handleItemToggle = (item) => {
    setSelectedItems(prev => 
      prev.includes(item) 
        ? prev.filter(i => i !== item)
        : [...prev, item]
    );
  };

  return (
    <div className="info-service">
      <div className="service-header">
        <p className="service-description">
          以下事项属于全程网办，可根据实际信息化服务需求选择多事项一次办
        </p>
      </div>

      <div className="service-list">
        {serviceCategories.map((category, categoryIndex) => (
          <div key={categoryIndex} className="service-category">
            <div className="category-header">
              <h3 className="category-title">{category.title}</h3>
            </div>
            
            {category.items.map((item, index) => (
              <div key={index} className="service-item">
                <div className="item-content">
                  <div 
                    className={`checkbox ${selectedItems.includes(item) ? 'checked' : ''}`}
                    onClick={() => handleItemToggle(item)}
                  >
                    {selectedItems.includes(item) && <span className="checkmark">✓</span>}
                  </div>
                  <span className="item-name">{item}</span>
                </div>
                <button className="guide-link">办事指南</button>
              </div>
            ))}
          </div>
        ))}
      </div>

      <div className="service-footer">
        <span className="selected-count">
          已选择{selectedItems.length}个事项
        </span>
        <button className="submit-button">立即办理</button>
      </div>
    </div>
  );
}

export default InfoService;