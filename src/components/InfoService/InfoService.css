.info-service {
  padding: 12px 36px;
  background-color: #eef6fe;
  border: 1px solid #a8d0fd;
  border-radius: 10px;
}

.service-description {
  color: #f59a23;
  font-size: 14px;
  margin: 0;
  padding: 12px 0;
  border-radius: 4px;
}

.service-list {
  display: flex;
  flex-direction: column;
  gap: 1px;
  margin-bottom: 30px;
}

.service-category {
  margin-bottom: 20px;
}

.category-header {
  margin-bottom: 10px;
}

.category-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
  padding: 10px 0;
  border-bottom: 2px solid #e9ecef;
}

.service-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  transition: background-color 0.2s ease;
}


.item-content {
  display: flex;
  align-items: center;
  gap: 15px;
  flex: 1;
}

.checkbox {
  width: 20px;
  height: 20px;
  border: 2px solid #ddd;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  background: white;
}

.checkbox.checked {
  background: #28a745;
  border-color: #28a745;
  color: white;
}

.checkbox:hover {
  border-color: #28a745;
}

.checkmark {
  font-size: 12px;
  font-weight: bold;
}

.item-name {
  font-size: 15px;
  color: #333;
  font-weight: 500;
}

.guide-link {
  font-weight: 400;
  background: transparent;
  border: none;
  color: #0000FF;
  font-size: 14px;
  cursor: pointer;
}

.service-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
  border-top: 1px solid #e9ecef;
}

.selected-count {
  color: #ff9500;
  font-size: 14px;
  font-weight: 500;
}

.submit-button {
  background: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.submit-button:hover {
  background: #0056b3;
}

.submit-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

/* Responsive */
@media (max-width: 768px) {
  .service-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .item-content {
    width: 100%;
  }
  
  .service-footer {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .submit-button {
    width: 100%;
  }
  
  .category-title {
    font-size: 14px;
  }
}
