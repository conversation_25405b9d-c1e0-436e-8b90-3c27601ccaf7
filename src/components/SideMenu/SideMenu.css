.side-menu {
  position: fixed;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 2px;
  z-index: 1000;
}

.menu-item {
  width: 80px;
  height: 80px;
  background: white;
  border: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.menu-item:hover {
  background: #f0f8ff;
  border-color: #74b9ff;
  transform: translateX(-5px);
}

.menu-item:first-child {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.menu-item:last-child {
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

.menu-icon {
  font-size: 24px;
  margin-bottom: 4px;
}

.menu-text {
  font-size: 12px;
  color: #666;
  text-align: center;
  font-weight: 500;
}

.menu-item:hover .menu-text {
  color: #74b9ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .side-menu {
    right: 10px;
  }
  
  .menu-item {
    width: 60px;
    height: 60px;
  }
  
  .menu-icon {
    font-size: 20px;
  }
  
  .menu-text {
    font-size: 10px;
  }
}
