import React from 'react';
import { useNavigate } from 'react-router-dom';
import './SideMenu.css';

function SideMenu() {
  const navigate = useNavigate();

  const menuItems = [
    {
      id: 1,
      icon: '🎧',
      text: '常见问题',
      path: '/faq'
    },
    {
      id: 2,
      icon: '📊',
      text: '进度查询',
      path: '/process-search'
    },
    {
      id: 3,
      icon: '📝',
      text: '服务评价',
      path: '/service-evaluation'
    },
    {
      id: 4,
      icon: '💬',
      text: '系统投诉',
      path: '/complaint'
    },
    {
      id: 5,
      icon: '↗',
      text: '返回顶部',
      path: null
    }
  ];

  const handleMenuClick = (item) => {
    if (item.text === '返回顶部') {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    } else if (item.path) {
      navigate(item.path);
    }
  };

  return (
    <div className="side-menu">
      {menuItems.map((item) => (
        <div 
          key={item.id} 
          className="menu-item"
          onClick={() => handleMenuClick(item)}
        >
          <div className="menu-icon">{item.icon}</div>
          <div className="menu-text">{item.text}</div>
        </div>
      ))}
    </div>
  );
}

export default SideMenu;
