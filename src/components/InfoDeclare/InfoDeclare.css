.project-list {
  background-color: #eef6fe;
  border: 1px solid #a8d0fd;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding:12px 36px;
}

.project-card {
  overflow: hidden;
  transition: all 0.3s ease;
  border-bottom: 1px solid #a8d0fd;
}

.project-card:last-child {
  border-bottom: none;
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
}

.project-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.project-name {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.project-tag {
  background: #fbe6cf;
  color: #f1a04e;
  border: 1px solid #f1a04e;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.project-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.guide-button {
  font-weight: 400;
  background: transparent;
  border: none;
  color: #0000FF;
  font-size: 14px;
  cursor: pointer;
}

.apply-button {
  font-weight: 700;
  background: linear-gradient(90deg, rgba(22, 198, 255, 1) 0%, rgba(22, 103, 255, 1) 100%);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.apply-button:hover {
  background: #0984e3;
}

.project-details {
  background: #fff;
  border-radius: 20px;
  padding: 12px 24px;
  margin-bottom: 12px;
}

.guide-section h4 {
  color: #333;
  font-size: 16px;
  margin-bottom: 15px;
  font-weight: 600;
}

.details-content {
  color: #666;
  line-height: 1.6;
}

.detail-item {
  margin-bottom: 8px;
  font-size: 14px;
}

.detail-item strong {
  color: #333;
}

/* Responsive */
@media (max-width: 768px) {
  .project-header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }
  
  .project-actions {
    align-self: stretch;
    justify-content: space-between;
  }
}