import React, { useState, useEffect } from 'react';
import { Tag } from 'antd'
import { getServiceType } from '../../api/info';
import { useRequest } from "ahooks";
import './InfoDeclare.css';

function InfoDeclare() {
  const projects = [
    {
      name: '市级数字化项目',
      tag: '全市统筹',
      guide: '办事指南',
      details: [
        '申报清单',
        '申报项目范围：各部门依据市政府信息化主管部门要求，结合学校信息化发展规划，用信息化手段提升管理的目标、范围明确的项目。',
        '申报项目要求：有完整的技术人员和经费保障，同时具有先进性技术方案和较好的应用基础环境。',
        '市场监督与技术方案：对于涉及上层技术或者产品合作开发时，初定主要的技术方案和市场需求分析。'
      ]
    },
    {
      name: '地高大',
      tag: '学院统筹',
      guide: '办事指南',
      details: [
        '申报清单',
        '适合学校的IT：',
        '根据《教育系统信息化建设规划》要求《教育信息化建设可行性研究》等，结合项目的具体建设等级、目标、内容、方案、实施计划等。',
        '申报文件要求完整人员配备清单。',
        '技术支持要求：',
        '基础学校管理（公共管理等基础）（G7）办公平台"信息化建设管理系统"）建立完善的技术队伍。',
        '项目监督材料与管理门公安部门安全保障基金部门（信息技术办公室、科技处等）。'
      ]
    },
    {
      name: '一网通办',
      tag: '全市统筹',
      guide: '办事指南'
    },
    {
      name: '其他系统',
      tag: '学院统筹',
      guide: '办事指南'
    }
  ];

  const { data: serviceTypes, loading, error } = useRequest(getServiceType, {
    defaultParams: [{ ssfl: '信息化申报' }],
    onSuccess: (res) => {
      console.log('获取服务类型成功:', res);
    },
  });
  // 移除 useEffect，让 useRequest 自动管理请求

  return (
    <div className="project-list">
      {projects.map((project, index) => (
        <div key={index} className="project-card">
          <div className="project-header">
            <div className="project-info">
              <h3 className="project-name">{project.name}</h3>
              <span className="project-tag">{project.tag}</span>
            </div>
            <div className="project-actions">
              <button className="guide-button">{project.guide}</button>
              <button className="apply-button">立即办理 ></button>
            </div>
          </div>

          {project.details && (
            <div className="project-details">
              <div className="guide-section">
                <h4>办事指南</h4>
                <div className="details-content">
                  {project.details.map((detail, idx) => (
                    <div key={idx} className="detail-item">
                      {detail.includes('：') ? (
                        <div>
                          <strong>{detail.split('：')[0]}：</strong>
                          {detail.split('：')[1]}
                        </div>
                      ) : (
                        <div>{detail}</div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  );
}

export default InfoDeclare;
