.process-search {
  padding: 20px;
}

.process-tabs .ant-tabs-nav {
  margin-bottom: 20px;
}

.process-tabs .ant-tabs-tab {
  font-size: 16px;
  font-weight: 500;
}

.status-tabs {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.status-tab {
  padding: 8px 16px;
  background: #f5f5f5;
  border-radius: 20px;
  font-size: 14px;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
}

.status-tab.active {
  background: #1890ff;
  color: white;
}

.status-tab:hover:not(.active) {
  background: #e6f7ff;
  color: #1890ff;
}

.process-table {
  background: white;
  border-radius: 8px;
}

.process-table .ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
  color: #333;
  border-bottom: 2px solid #f0f0f0;
}

.process-table .ant-table-tbody > tr > td {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.process-table .ant-table-tbody > tr:hover > td {
  background: #f5f5f5;
}

.process-link {
  color: #1890ff;
  text-decoration: none;
}

.process-link:hover {
  color: #40a9ff;
  text-decoration: underline;
}

.view-button {
  padding: 0;
  font-size: 16px;
  color: #1890ff;
}

.view-button:hover {
  color: #40a9ff;
}

.tab-placeholder {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.tab-placeholder h3 {
  color: #333;
  margin-bottom: 20px;
  font-size: 18px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .process-search {
    padding: 10px;
  }
  
  .status-tabs {
    flex-direction: column;
    gap: 10px;
  }
  
  .process-table .ant-table-thead > tr > th,
  .process-table .ant-table-tbody > tr > td {
    padding: 8px 12px;
    font-size: 12px;
  }
  
  .process-tabs .ant-tabs-tab {
    font-size: 14px;
  }
}