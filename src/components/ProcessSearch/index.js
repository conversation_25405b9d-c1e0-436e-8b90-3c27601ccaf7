import React, { useState } from 'react';
import { Table, Tabs, Button } from 'antd';
import { useNavigate } from 'react-router-dom';
import './ProcessSearch.css';

const { TabPane } = Tabs;

function ProcessSearch() {
  const [activeTab, setActiveTab] = useState('progress');
  const navigate = useNavigate();

  // 模拟进度数据
  const progressData = [
    {
      key: '1',
      serialNumber: '1',
      serviceName: '马克思电子证照申请',
      businessNumber: 'YZ20250008',
      serviceProcess: '电子证照申请',
      processTime: '2025-04-27',
      completionTime: '2025-04-27',
      duration: '1分17秒'
    },
    {
      key: '2',
      serialNumber: '2',
      serviceName: '外（返）聘教师（专家）申请',
      businessNumber: 'WP20250200',
      serviceProcess: '马克思（返）聘教师（专家）申请',
      currentNode: '校聘委会议审议',
      completionTime: '2025-04-30',
      action: '查看'
    },
    {
      key: '3',
      serialNumber: '3',
      serviceName: '收入证明打印',
      businessNumber: 'SR20250009',
      serviceProcess: '马克思收入证明打印',
      currentNode: '人事处审核',
      completionTime: '2025-04-17',
      action: '查看'
    },
    {
      key: '4',
      serialNumber: '4',
      serviceName: '中层干部因私出国（境）审批',
      businessNumber: 'HP20250005',
      serviceProcess: '马克思出国审批',
      currentNode: '申请人信息',
      completionTime: '2025-04-07',
      action: '查看'
    },
    {
      key: '5',
      serialNumber: '5',
      serviceName: '自费出书备案',
      businessNumber: 'RK20250006',
      serviceProcess: '马克思书籍备案',
      currentNode: '部门负责人审核',
      completionTime: '2025-03-31',
      action: '查看'
    },
    {
      key: '6',
      serialNumber: '6',
      serviceName: '校园卡挂失补办',
      businessNumber: 'BX20250067',
      serviceProcess: '马克思校园卡挂失补办',
      currentNode: '申请',
      completionTime: '2025-03-25',
      action: '查看'
    },
    {
      key: '7',
      serialNumber: '7',
      serviceName: '通讯录信息维护申请',
      businessNumber: 'JA20250009',
      serviceProcess: '马克思通讯录信息维护申请',
      currentNode: '部门负责人审核',
      completionTime: '2025-03-25',
      action: '查看'
    }
  ];

  const columns = [
    {
      title: '序号',
      dataIndex: 'serialNumber',
      key: 'serialNumber',
      width: 60,
      align: 'center'
    },
    {
      title: '事务名称',
      dataIndex: 'serviceName',
      key: 'serviceName',
      width: 180,
      render: (text) => (
        <a href="#" className="service-link">{text}</a>
      )
    },
    {
      title: '业务编号',
      dataIndex: 'businessNumber',
      key: 'businessNumber',
      width: 120
    },
    {
      title: '事务流程',
      dataIndex: 'serviceProcess',
      key: 'serviceProcess',
      width: 150
    },
    {
      title: '处理时间',
      dataIndex: 'processTime',
      key: 'processTime',
      width: 120
    },
    {
      title: '办结时间',
      dataIndex: 'completionTime',
      key: 'completionTime',
      width: 120
    },
    {
      title: '耗时用时',
      dataIndex: 'duration',
      key: 'duration',
      width: 100
    }
  ];

  const handleTabChange = (key) => {
    setActiveTab(key);
    // 根据tab切换到对应页面
    switch (key) {
      case 'progress':
        // 当前页面，不需要跳转
        break;
      case 'evaluation':
        navigate('/service-evaluation');
        break;
      case 'complaint':
        navigate('/complaint');
        break;
      default:
        break;
    }
  };

  return (
    <div className="process-search">
      <Tabs 
        activeKey={activeTab} 
        onChange={handleTabChange}
        className="process-tabs"
      >
        <TabPane tab="进度查询" key="progress">
          <div className="status-tabs">
            <div className="status-tab active">
              办理中 (5)
            </div>
            <div className="status-tab">
              已办结 (8)
            </div>
          </div>
          
          <Table
            columns={columns}
            dataSource={progressData}
            pagination={false}
            className="process-table"
            size="middle"
          />
        </TabPane>
        
        <TabPane tab="服务评价" key="evaluation">
          <div className="tab-placeholder">
            <h3>服务评价</h3>
            <p>正在跳转到服务评价页面...</p>
          </div>
        </TabPane>
        
        <TabPane tab="系统投诉" key="complaint">
          <div className="tab-placeholder">
            <h3>系统投诉</h3>
            <p>正在跳转到系统投诉页面...</p>
          </div>
        </TabPane>
      </Tabs>
    </div>
  );
}

export default ProcessSearch;
