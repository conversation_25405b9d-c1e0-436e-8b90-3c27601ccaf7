
import React, { useState } from 'react';
import './InfoApply.css';
import { getServiceType } from '../../api/info';
import { useRequest } from "ahooks";

function InfoApply() {
  const [selectedItems, setSelectedItems] = useState([
    '固定IP地址申请',
    '对接校数据中心主数据库',
    '对接统一身份认证系统',
    '对接统一通讯平台',
    '对接信息门户待办事宜',
    '对接电子签章'
  ]);

  const allItems = [
    '堡垒机账户开设',
    '虚拟主机新建/变更',
    '校园网域名新增/变更/备案',
    '固定IP地址申请',
    '对接校数据中心主数据库',
    '对接统一身份认证系统',
    '对接统一通讯平台',
    '对接信息门户待办事宜',
    '对接电子签章'
  ];

    const { data: serviceTypes, loading, error } = useRequest(getServiceType, {
    defaultParams: [{ ssfl: '信息化资源' }],
    onSuccess: (res) => {
      console.log('获取服务类型成功:', res);
    },
  });

  const handleItemToggle = (item) => {
    setSelectedItems(prev => 
      prev.includes(item) 
        ? prev.filter(i => i !== item)
        : [...prev, item]
    );
  };

  return (
    <div className="info-apply">
      <div className="apply-header">
        <p className="apply-description">
          以下事项属于全程网办，可根据实际信息化资源需求选择多事项一次办
        </p>
      </div>

      <div className="apply-list">
        {allItems.map((item, index) => (
          <div key={index} className="apply-item">
            <div className="item-content">
              <div 
                className={`checkbox ${selectedItems.includes(item) ? 'checked' : ''}`}
                onClick={() => handleItemToggle(item)}
              >
                {selectedItems.includes(item) && <span className="checkmark">✓</span>}
              </div>
              <span className="item-name">{item}</span>
            </div>
            <button className="guide-link">办事指南</button>
          </div>
        ))}
      </div>

      <div className="apply-footer">
        <span className="selected-count">
          已选择{selectedItems.length}个事项
        </span>
        <button className="submit-button">立即办理</button>
      </div>
    </div>
  );
}

export default InfoApply;

