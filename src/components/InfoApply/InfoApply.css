.info-apply {
  padding: 12px 36px;
  background-color: #eef6fe;
  border: 1px solid #a8d0fd;
  border-radius: 10px;
}


.apply-description {
  color: #f59a23;
  font-size: 14px;
  margin: 0;
  padding: 12px 0;
  border-radius: 4px;
}

.apply-list {
  display: flex;
  flex-direction: column;
  gap: 1px;
  margin-bottom: 30px;
}

.apply-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #a8d0fd;
  transition: background-color 0.2s ease;
}

.item-content {
  display: flex;
  align-items: center;
  gap: 15px;
  flex: 1;
}

.checkbox {
  width: 20px;
  height: 20px;
  border: 2px solid #ddd;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  background: white;
}

.checkbox.checked {
  background: #28a745;
  border-color: #28a745;
  color: white;
}

.checkbox:hover {
  border-color: #28a745;
}

.checkmark {
  font-size: 12px;
  font-weight: bold;
}

.item-name {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.guide-link {
  font-weight: 400;
  background: transparent;
  border: none;
  color: #0000FF;
  font-size: 14px;
  cursor: pointer;
}

.apply-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.selected-count {
  color: #ff9500;
  font-size: 14px;
  font-weight: 500;
}

.submit-button {
  background: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.submit-button:hover {
  background: #0056b3;
}

.submit-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

/* Responsive */
@media (max-width: 768px) {
  .apply-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .item-content {
    width: 100%;
  }

  .apply-footer {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .submit-button {
    width: 100%;
  }
}
