import React, { useState, useEffect } from 'react';
import { Form, Input, Select, Button, Table, Upload, message, Tabs } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import './SystemComplaint.css';

const { TextArea } = Input;
const { Option } = Select;
const { TabPane } = Tabs;

function SystemComplaint() {
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState('complaint');
  const [statusTab, setStatusTab] = useState('pending');
  const navigate = useNavigate();
  const location = useLocation();

  // 根据来源设置正确的tab
  useEffect(() => {
    setActiveTab('complaint');
  }, [location]);

  const handleTabChange = (key) => {
    setActiveTab(key);
    switch (key) {
      case 'progress':
        navigate('/process-search');
        break;
      case 'evaluation':
        navigate('/service-evaluation');
        break;
      case 'complaint':
        // 当前页面，不需要跳转
        break;
      default:
        break;
    }
  };

  // 模拟投诉数据
  const complaintData = [
    {
      key: '1',
      serialNumber: '1',
      systemName: '教务系统',
      problemDescription: '登录报错',
      reportTime: '2025-04-27',
      action: '查看'
    },
    {
      key: '2',
      serialNumber: '2',
      systemName: '学工系统',
      problemDescription: '登录报错',
      reportTime: '2025-04-27',
      action: '查看'
    },
    {
      key: '3',
      serialNumber: '3',
      systemName: '一网通办',
      problemDescription: '登录报错',
      reportTime: '2025-04-27',
      action: '查看'
    },
    {
      key: '4',
      serialNumber: '4',
      systemName: '一网通办',
      problemDescription: '登录报错',
      reportTime: '2025-04-27',
      action: '查看'
    },
    {
      key: '5',
      serialNumber: '5',
      systemName: '一网通办',
      problemDescription: '登录报错',
      reportTime: '2025-04-25',
      action: '查看'
    },
    {
      key: '6',
      serialNumber: '6',
      systemName: '一网通办',
      problemDescription: '登录报错',
      reportTime: '2025-04-23',
      action: '查看'
    }
  ];

  const columns = [
    {
      title: '序号',
      dataIndex: 'serialNumber',
      key: 'serialNumber',
      width: 80,
      align: 'center'
    },
    {
      title: '系统名称',
      dataIndex: 'systemName',
      key: 'systemName',
      width: 150,
      render: (text) => (
        <a href="#" className="system-link">{text}</a>
      )
    },
    {
      title: '问题描述',
      dataIndex: 'problemDescription',
      key: 'problemDescription',
      width: 200
    },
    {
      title: '反馈时间',
      dataIndex: 'reportTime',
      key: 'reportTime',
      width: 120
    },
    {
      title: '查看',
      key: 'action',
      width: 80,
      align: 'center',
      render: () => (
        <Button type="link" className="view-button">
          🔍
        </Button>
      )
    }
  ];

  const handleSubmit = (values) => {
    console.log('投诉提交:', values);
    message.success('投诉提交成功！');
    form.resetFields();
  };

  const uploadProps = {
    name: 'file',
    action: '/upload',
    headers: {
      authorization: 'authorization-text',
    },
    onChange(info) {
      if (info.file.status === 'done') {
        message.success(`${info.file.name} 文件上传成功`);
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} 文件上传失败`);
      }
    },
  };

  return (
    <div className="system-complaint">
      <Tabs 
        activeKey={activeTab} 
        onChange={handleTabChange}
        className="complaint-tabs"
      >
        <TabPane tab="进度查询" key="progress">
          <div className="tab-placeholder">
            <h3>进度查询</h3>
            <p>正在跳转到进度查询页面...</p>
          </div>
        </TabPane>
        
        <TabPane tab="服务评价" key="evaluation">
          <div className="tab-placeholder">
            <h3>服务评价</h3>
            <p>正在跳转到服务评价页面...</p>
          </div>
        </TabPane>
        
        <TabPane tab="系统投诉" key="complaint">
          <div className="complaint-form-section">
            <h3 className="section-title">系统故障信息</h3>
            
            <Form
              form={form}
              layout="vertical"
              onFinish={handleSubmit}
              className="complaint-form"
            >
              <div className="form-row">
                <div className="form-item-half">
                  <Form.Item label="报障人姓名" name="reporterName" initialValue="张三">
                    <Input disabled />
                  </Form.Item>
                </div>
                <div className="form-item-half">
                  <Form.Item label="报障人手机号" name="reporterPhone" initialValue="15011112222">
                    <Input disabled />
                  </Form.Item>
                </div>
              </div>

              <Form.Item label="报障人时间" name="reportTime" initialValue="2025-06-30 12:00">
                <Input disabled />
              </Form.Item>

              <Form.Item 
                label="系统名称" 
                name="systemName"
                rules={[{ required: true, message: '请选择系统名称' }]}
              >
                <Select placeholder="请选择系统名称">
                  <Option value="教务系统">教务系统</Option>
                  <Option value="学工系统">学工系统</Option>
                  <Option value="一网通办">一网通办</Option>
                  <Option value="财务系统">财务系统</Option>
                  <Option value="人事系统">人事系统</Option>
                </Select>
              </Form.Item>

              <Form.Item 
                label="问题描述" 
                name="problemDescription"
                rules={[{ required: true, message: '请输入问题描述' }]}
              >
                <TextArea 
                  rows={4} 
                  placeholder="请详细描述遇到的问题"
                  maxLength={500}
                  showCount
                />
              </Form.Item>

              <Form.Item label="故障附件" name="attachment">
                <Upload {...uploadProps}>
                  <Button icon={<UploadOutlined />}>选择文件</Button>
                </Upload>
              </Form.Item>

              <Form.Item>
                <Button type="primary" htmlType="submit" className="submit-button">
                  提交故障信息
                </Button>
              </Form.Item>
            </Form>
          </div>

          <div className="complaint-list-section">
            <div className="status-tabs">
              <div 
                className={`status-tab ${statusTab === 'pending' ? 'active' : ''}`}
                onClick={() => setStatusTab('pending')}
              >
                待解决 (5)
              </div>
              <div 
                className={`status-tab ${statusTab === 'resolved' ? 'active' : ''}`}
                onClick={() => setStatusTab('resolved')}
              >
                已解决 (8)
              </div>
            </div>

            <Table
              columns={columns}
              dataSource={complaintData}
              pagination={false}
              className="complaint-table"
              size="middle"
            />
          </div>
        </TabPane>
      </Tabs>
    </div>
  );
}

export default SystemComplaint;
