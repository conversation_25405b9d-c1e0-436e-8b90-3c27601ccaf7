import axios from 'axios'
import { message } from 'antd'
const service = axios.create({
  baseURL: ``,
  timeout: 20000
})

const err = (error) => {
  if (error.response) {
    switch (error.response.status) {
      case 401: {
        const userInfo = localStorage.getItem('userInfo')
        message.warning('认证失败请重新登录')
        if (userInfo) {
          localStorage.removeItem('userInfo')
          localStorage.removeItem('token')
          setTimeout(() => {
            window.location.reload()
          }, 1000)
        }
        break
      }
      case 404:
        message.error('未找到资源')
        break
      case 400:
        message.error('错误的请求')
        break
      case 405:
        message.error('Method Not Allowed')
        break
      default:
        message.error(error.response.statusText)
        break
    }
  }
  return Promise.reject(error)
}

service.interceptors.request.use((config) => {
  console.log(config);
  return config;
}, err);

service.interceptors.response.use((response) => {
  if (response.status === 200) {
    return Promise.resolve(response);
  }
  return Promise.reject(response);
}, err);


export default service;

